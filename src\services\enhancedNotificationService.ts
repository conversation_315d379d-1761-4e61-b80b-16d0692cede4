/**
 * Enhanced Notification Service
 *
 * @deprecated This service is deprecated. Use integratedNotificationService.ts instead.
 *
 * This service handles both in-app and email notifications with user preferences.
 * It integrates with the existing notification system and adds email capabilities.
 */

import { supabase } from '../lib/supabase'
import { 
  sendConnectionRequestEmail, 
  sendConnectionAcceptedEmail, 
  sendPostLikeEmail, 
  sendPostCommentEmail, 
  sendNewMessageEmail 
} from './emailService'

// Types for notification data
export interface NotificationData {
  userId: string
  title: string
  content: string
  type: string
  relatedEntityId?: string
  emailData?: {
    firstName?: string
    lastName?: string
    email?: string
    // Connection-specific
    requesterName?: string
    requesterProfileUrl?: string
    // Post-specific
    postTitle?: string
    postUrl?: string
    likerName?: string
    commenterName?: string
    commentText?: string
    // Message-specific
    senderName?: string
    messagePreview?: string
    conversationUrl?: string
  }
}

// Types for email preferences
export interface EmailPreferences {
  connection_requests: boolean
  connection_accepted: boolean
  post_likes: boolean
  post_comments: boolean
  new_messages: boolean
  email_enabled: boolean
}

/**
 * Creates both in-app and email notifications based on user preferences
 * @param notificationData The notification data
 * @returns Promise with success status
 */
export async function createNotification(notificationData: NotificationData): Promise<{ success: boolean; error?: string }> {
  try {
    // Create in-app notification
    const { error: inAppError } = await supabase
      .from('user_notifications')
      .insert({
        user_id: notificationData.userId,
        title: notificationData.title,
        content: notificationData.content,
        type: notificationData.type,
        related_entity_id: notificationData.relatedEntityId,
        is_read: false
      })

    if (inAppError) {
      console.error('Error creating in-app notification:', inAppError)
      return { success: false, error: inAppError.message }
    }

    // Check if email notification should be sent
    if (notificationData.emailData?.email) {
      await sendEmailNotificationIfEnabled(notificationData)
    }

    return { success: true }
  } catch (error: any) {
    console.error('Error in createNotification:', error)
    return { success: false, error: error.message }
  }
}

/**
 * Sends email notification if user has enabled it for this type
 * @param notificationData The notification data
 */
async function sendEmailNotificationIfEnabled(notificationData: NotificationData): Promise<void> {
  try {
    // Get user's email preferences
    const { data: preferences, error: prefError } = await supabase
      .from('email_notification_preferences')
      .select('*')
      .eq('user_id', notificationData.userId)
      .single()

    if (prefError) {
      console.log('No email preferences found for user, using defaults')
      // If no preferences exist, create default ones and proceed
      await createDefaultEmailPreferences(notificationData.userId)
    }

    const emailPrefs = preferences || getDefaultPreferences()

    // Check if email notifications are globally enabled
    if (!emailPrefs.email_enabled) {
      console.log('Email notifications disabled for user')
      return
    }

    // Check if this specific notification type is enabled
    const typeEnabled = getNotificationTypeEnabled(emailPrefs, notificationData.type)
    if (!typeEnabled) {
      console.log(`Email notifications disabled for type: ${notificationData.type}`)
      return
    }

    // Send the appropriate email notification
    await sendEmailByType(notificationData)

    // Log the email notification
    await logEmailNotification(notificationData)

  } catch (error) {
    console.error('Error sending email notification:', error)
  }
}

/**
 * Creates default email preferences for a user
 * @param userId The user ID
 */
async function createDefaultEmailPreferences(userId: string): Promise<void> {
  try {
    await supabase
      .from('email_notification_preferences')
      .insert({ user_id: userId })
  } catch (error) {
    console.error('Error creating default email preferences:', error)
  }
}

/**
 * Gets default email preferences
 * @returns Default preferences object
 */
function getDefaultPreferences(): EmailPreferences {
  return {
    connection_requests: true,
    connection_accepted: true,
    post_likes: true,
    post_comments: true,
    new_messages: true,
    email_enabled: true
  }
}

/**
 * Checks if a notification type is enabled in user preferences
 * @param preferences The user's email preferences
 * @param notificationType The notification type
 * @returns True if enabled, false otherwise
 */
function getNotificationTypeEnabled(preferences: any, notificationType: string): boolean {
  const typeMap: { [key: string]: string } = {
    'connection_request': 'connection_requests',
    'connection_accepted': 'connection_accepted',
    'post_like': 'post_likes',
    'post_comment': 'post_comments',
    'new_message': 'new_messages'
  }

  const prefKey = typeMap[notificationType]
  return prefKey ? preferences[prefKey] : false
}

/**
 * Sends email notification based on type
 * @param notificationData The notification data
 */
async function sendEmailByType(notificationData: NotificationData): Promise<void> {
  const { emailData } = notificationData
  if (!emailData?.email) return

  try {
    switch (notificationData.type) {
      case 'connection_request':
        if (emailData.requesterName && emailData.requesterProfileUrl) {
          await sendConnectionRequestEmail(
            emailData.email,
            emailData.requesterName,
            emailData.requesterProfileUrl,
            emailData.firstName
          )
        }
        break

      case 'connection_accepted':
        if (emailData.requesterName && emailData.requesterProfileUrl) {
          await sendConnectionAcceptedEmail(
            emailData.email,
            emailData.requesterName,
            emailData.requesterProfileUrl,
            emailData.firstName
          )
        }
        break

      case 'post_like':
        if (emailData.likerName && emailData.postTitle && emailData.postUrl) {
          await sendPostLikeEmail(
            emailData.email,
            emailData.likerName,
            emailData.postTitle,
            emailData.postUrl,
            emailData.firstName
          )
        }
        break

      case 'post_comment':
        if (emailData.commenterName && emailData.postTitle && emailData.postUrl) {
          await sendPostCommentEmail(
            emailData.email,
            emailData.commenterName,
            emailData.postTitle,
            emailData.commentText || '',
            emailData.postUrl,
            emailData.firstName
          )
        }
        break

      case 'new_message':
        if (emailData.senderName && emailData.conversationUrl) {
          await sendNewMessageEmail(
            emailData.email,
            emailData.senderName,
            emailData.messagePreview || '',
            emailData.conversationUrl,
            emailData.firstName
          )
        }
        break

      default:
        console.log(`No email handler for notification type: ${notificationData.type}`)
    }
  } catch (error) {
    console.error(`Error sending ${notificationData.type} email:`, error)
  }
}

/**
 * Logs email notification to the database
 * @param notificationData The notification data
 */
async function logEmailNotification(notificationData: NotificationData): Promise<void> {
  try {
    await supabase
      .from('notification_email_logs')
      .insert({
        user_id: notificationData.userId,
        email_address: notificationData.emailData?.email,
        subject: notificationData.title,
        template_name: notificationData.type,
        notification_type: notificationData.type,
        related_entity_id: notificationData.relatedEntityId,
        status: 'sent'
      })
  } catch (error) {
    console.error('Error logging email notification:', error)
  }
}

/**
 * Gets user's email preferences
 * @param userId The user ID
 * @returns Email preferences or null if not found
 */
export async function getUserEmailPreferences(userId: string): Promise<EmailPreferences | null> {
  try {
    const { data, error } = await supabase
      .from('email_notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error) {
      console.error('Error fetching email preferences:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in getUserEmailPreferences:', error)
    return null
  }
}

/**
 * Updates user's email preferences
 * @param userId The user ID
 * @param preferences The new preferences
 * @returns Success status
 */
export async function updateUserEmailPreferences(
  userId: string, 
  preferences: Partial<EmailPreferences>
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('email_notification_preferences')
      .upsert({
        user_id: userId,
        ...preferences,
        updated_at: new Date().toISOString()
      })

    if (error) {
      console.error('Error updating email preferences:', error)
      return { success: false, error: error.message }
    }

    return { success: true }
  } catch (error: any) {
    console.error('Error in updateUserEmailPreferences:', error)
    return { success: false, error: error.message }
  }
}
