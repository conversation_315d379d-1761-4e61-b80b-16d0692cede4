<template>
  <q-card class="ai-features-card">
    <q-card-section class="bg-gradient-primary text-white">
      <div class="row items-center">
        <q-icon name="psychology" size="32px" class="q-mr-md" />
        <div>
          <div class="text-h6">AI-Powered Features</div>
          <div class="text-subtitle2">Enhance your innovation journey with AI</div>
        </div>
      </div>
    </q-card-section>

    <q-card-section>
      <div class="row q-col-gutter-md">
        <!-- Profile Analysis -->
        <div class="col-12 col-md-6">
          <q-card flat bordered class="feature-card">
            <q-card-section>
              <div class="row items-center q-mb-sm">
                <q-icon name="person_search" color="primary" size="24px" class="q-mr-sm" />
                <div class="text-subtitle1 text-weight-medium">Profile Analysis</div>
              </div>
              <p class="text-body2 q-mb-md">
                Get AI-powered suggestions to optimize your profile and attract better matches.
              </p>
              <q-btn
                @click="analyzeProfile"
                color="primary"
                outline
                size="sm"
                :loading="analyzingProfile"
                class="full-width"
              >
                Analyze My Profile
              </q-btn>
            </q-card-section>
          </q-card>
        </div>

        <!-- Content Ideas -->
        <div class="col-12 col-md-6">
          <q-card flat bordered class="feature-card">
            <q-card-section>
              <div class="row items-center q-mb-sm">
                <q-icon name="lightbulb" color="primary" size="24px" class="q-mr-sm" />
                <div class="text-subtitle1 text-weight-medium">Content Ideas</div>
              </div>
              <p class="text-body2 q-mb-md">
                Generate engaging content ideas tailored to your expertise and interests.
              </p>
              <q-btn
                @click="generateIdeas"
                color="primary"
                outline
                size="sm"
                :loading="generatingIdeas"
                class="full-width"
              >
                Get Content Ideas
              </q-btn>
            </q-card-section>
          </q-card>
        </div>

        <!-- Innovation Insights -->
        <div class="col-12 col-md-6">
          <q-card flat bordered class="feature-card">
            <q-card-section>
              <div class="row items-center q-mb-sm">
                <q-icon name="trending_up" color="primary" size="24px" class="q-mr-sm" />
                <div class="text-subtitle1 text-weight-medium">Innovation Insights</div>
              </div>
              <p class="text-body2 q-mb-md">
                Stay updated with AI-curated insights about innovation trends in Zimbabwe.
              </p>
              <q-btn
                @click="getInsights"
                color="primary"
                outline
                size="sm"
                :loading="gettingInsights"
                class="full-width"
              >
                Get Insights
              </q-btn>
            </q-card-section>
          </q-card>
        </div>

        <!-- AI Chat -->
        <div class="col-12 col-md-6">
          <q-card flat bordered class="feature-card">
            <q-card-section>
              <div class="row items-center q-mb-sm">
                <q-icon name="psychology" color="primary" size="24px" class="q-mr-sm" />
                <div class="text-subtitle1 text-weight-medium">AI Assistant</div>
              </div>
              <p class="text-body2 q-mb-md">
                Chat with our AI assistant for personalized guidance and support.
              </p>
              <q-btn
                @click="openChat"
                color="primary"
                outline
                size="sm"
                class="full-width"
              >
                Start Chatting
              </q-btn>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </q-card-section>

    <!-- Results Dialog -->
    <q-dialog v-model="showResults" persistent>
      <q-card style="min-width: 400px; max-width: 600px;">
        <q-card-section class="row items-center">
          <q-icon :name="resultIcon" size="24px" :color="resultColor" class="q-mr-sm" />
          <div class="text-h6">{{ resultTitle }}</div>
          <q-space />
          <q-btn icon="close" flat round dense @click="showResults = false" />
        </q-card-section>

        <q-card-section>
          <div v-if="resultContent" class="text-body1" v-html="formatContent(resultContent)"></div>
          <div v-else-if="resultError" class="text-negative">
            {{ resultError }}
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Close" color="primary" @click="showResults = false" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import { useAuthStore } from '../../stores/auth';
import {
  sendEnhancedChatMessage,
  type EnhancedChatRequest,
  type EnhancedChatResponse
} from '../../services/aiEnhancedService';

// Composables
const $q = useQuasar();
const authStore = useAuthStore();

// Reactive state
const analyzingProfile = ref(false);
const generatingIdeas = ref(false);
const gettingInsights = ref(false);
const showResults = ref(false);
const resultTitle = ref('');
const resultContent = ref('');
const resultError = ref('');
const resultIcon = ref('');
const resultColor = ref('');

// Methods
const analyzeProfile = async () => {
  if (!authStore.user) {
    $q.notify({
      type: 'warning',
      message: 'Please sign in to use AI features',
      position: 'top'
    });
    return;
  }

  analyzingProfile.value = true;
  
  try {
    // Mock profile data - in real implementation, get from profile store
    const profileData = {
      profile_type: authStore.user.user_metadata?.profile_type || 'innovator',
      bio: 'Sample bio',
      skills: ['Innovation', 'Technology'],
      industries: ['Technology', 'Healthcare'],
      user_id: authStore.user.id
    };

    // Use enhanced AI service for profile analysis
    const analysisPrompt = `
      Analyze this user profile and provide 3-5 specific suggestions for improvement:

      Profile Type: ${profileData.profile_type}
      Bio: ${profileData.bio || 'Not provided'}
      Skills: ${profileData.skills?.join(', ') || 'Not provided'}
      Industries: ${profileData.industries?.join(', ') || 'Not provided'}

      Provide actionable suggestions to make this profile more attractive to potential matches and collaborators.
    `;

    const request: EnhancedChatRequest = {
      message: analysisPrompt,
      conversation_history: [],
      user_context: {
        is_authenticated: true,
        profile_type: profileData.profile_type,
        user_id: profileData.user_id,
        current_page: 'dashboard'
      }
    };

    const result = await sendEnhancedChatMessage(request);

    showResultDialog('Profile Analysis', result.response, result.error, 'person_search', 'primary');
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: 'Failed to analyze profile',
      position: 'top'
    });
  } finally {
    analyzingProfile.value = false;
  }
};

const generateIdeas = async () => {
  generatingIdeas.value = true;
  
  try {
    const userInterests = ['Innovation', 'Technology', 'Entrepreneurship'];
    const profileType = authStore.user?.user_metadata?.profile_type || 'innovator';
    
    // Use enhanced AI service for content ideas
    const contentPrompt = `
      Generate 5 content ideas for a ${profileType} interested in: ${userInterests.join(', ')}.

      The content should be relevant to Zimbabwe's innovation ecosystem and help build their professional presence.
      Format as a numbered list with brief descriptions.
    `;

    const request: EnhancedChatRequest = {
      message: contentPrompt,
      conversation_history: [],
      user_context: {
        is_authenticated: true,
        profile_type: profileType,
        current_page: 'dashboard'
      }
    };

    const result = await sendEnhancedChatMessage(request);

    showResultDialog('Content Ideas', result.response, result.error, 'lightbulb', 'primary');
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: 'Failed to generate content ideas',
      position: 'top'
    });
  } finally {
    generatingIdeas.value = false;
  }
};

const getInsights = async () => {
  gettingInsights.value = true;
  
  try {
    // Use enhanced AI service for innovation insights
    const insightsPrompt = `
      Provide insights about current innovation trends in Zimbabwe and Africa.
      Focus on emerging technologies, startup ecosystem developments, and opportunities for innovators.
      Keep it relevant and actionable for platform users.
    `;

    const request: EnhancedChatRequest = {
      message: insightsPrompt,
      conversation_history: [],
      user_context: {
        is_authenticated: true,
        current_page: 'dashboard'
      }
    };

    const result = await sendEnhancedChatMessage(request);

    showResultDialog('Innovation Insights', result.response, result.error, 'trending_up', 'primary');
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: 'Failed to get insights',
      position: 'top'
    });
  } finally {
    gettingInsights.value = false;
  }
};

const openChat = () => {
  // This would trigger the AI chat component to open
  // For now, show a message
  $q.notify({
    type: 'info',
    message: 'Look for the AI chat button in the bottom-right corner!',
    position: 'top',
    icon: 'smart_toy'
  });
};

const showResultDialog = (title: string, content: string, error: string, icon: string, color: string) => {
  resultTitle.value = title;
  resultContent.value = content;
  resultError.value = error;
  resultIcon.value = icon;
  resultColor.value = color;
  showResults.value = true;
};

const formatContent = (content: string) => {
  return content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/(\d+\.)/g, '<br><strong>$1</strong>');
};
</script>

<style scoped>
.ai-features-card {
  border-radius: 12px;
  overflow: hidden;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
}

.feature-card {
  height: 100%;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.feature-card .q-card-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.feature-card .q-btn {
  margin-top: auto;
}
</style>
