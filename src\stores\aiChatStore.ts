/**
 * AI Chat Store
 * 
 * Global state management for AI chat functionality
 * Provides centralized access to chat state, conversation history, and actions
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from './auth';
import { useRoute } from 'vue-router';
import { AIConversationService, type AIConversation, type AIMessage } from '../services/aiConversationService';
import {
  sendEnhancedChatMessage,
  sendEnhancedChatMessageStream,
  type EnhancedChatRequest,
  type ActionButton as EnhancedActionButton
} from '../services/aiEnhancedService';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  actions?: ActionButton[];
  suggestions?: string[];
}

export interface ActionButton {
  type: 'navigation' | 'action' | 'external';
  label: string;
  icon: string;
  url?: string;
  action?: string;
  color?: string;
}

export interface UserContext {
  is_authenticated: boolean;
  profile_type?: string;
  profile_completion?: number;
  current_page?: string;
  user_id?: string;
  profile_data?: any;
}

export const useAIChatStore = defineStore('aiChat', () => {
  // State
  const isOpen = ref(false);
  const isLoading = ref(false);
  const hasUnreadMessage = ref(false);
  const messages = ref<ChatMessage[]>([]);
  const conversationId = ref<string | null>(null);
  const currentConversation = ref<AIConversation | null>(null);
  const conversationHistory = ref<AIConversation[]>([]);
  const isLoadingHistory = ref(false);

  // Computed
  const authStore = useAuthStore();
  
  const userContext = computed((): UserContext => {
    const route = useRoute();
    
    return {
      is_authenticated: authStore.isAuthenticated,
      profile_type: authStore.user?.user_metadata?.profile_type,
      profile_completion: authStore.user?.user_metadata?.profile_completion || 0,
      current_page: route.name as string || 'unknown',
      user_id: authStore.user?.id,
      profile_data: authStore.user?.user_metadata
    };
  });

  const messageHistory = computed(() =>
    messages.value.map(msg => ({
      role: msg.role,
      content: msg.content
    }))
  );

  const lastMessage = computed(() => 
    messages.value.length > 0 ? messages.value[messages.value.length - 1] : null
  );

  // Actions
  const toggleChat = () => {
    isOpen.value = !isOpen.value;
    if (isOpen.value) {
      hasUnreadMessage.value = false;
    }
  };

  const openChat = () => {
    isOpen.value = true;
    hasUnreadMessage.value = false;
  };

  const closeChat = () => {
    isOpen.value = false;
  };

  const addMessage = (message: Omit<ChatMessage, 'id' | 'timestamp'>): ChatMessage => {
    const newMessage: ChatMessage = {
      ...message,
      id: crypto.randomUUID(),
      timestamp: new Date()
    };

    messages.value.push(newMessage);

    // Mark as unread if chat is closed and it's an assistant message
    if (!isOpen.value && message.role === 'assistant') {
      hasUnreadMessage.value = true;
    }

    // Save message to database if user is authenticated
    if (userContext.value.is_authenticated) {
      saveMessage(newMessage).catch(error => {
        console.error('Failed to save message:', error);
      });
    }

    return newMessage;
  };

  const updateMessage = (messageId: string, updates: Partial<ChatMessage>) => {
    const messageIndex = messages.value.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
      messages.value[messageIndex] = {
        ...messages.value[messageIndex],
        ...updates
      };
    }
  };

  const clearMessages = () => {
    messages.value = [];
    conversationId.value = null;
  };

  const setLoading = (loading: boolean) => {
    isLoading.value = loading;
  };

  const setConversationId = (id: string) => {
    conversationId.value = id;
  };

  // Persistence methods
  const loadOrCreateConversation = async () => {
    if (!userContext.value.is_authenticated || !userContext.value.user_id) {
      return;
    }

    try {
      isLoadingHistory.value = true;

      // Get or create conversation for the user
      const conversation = await AIConversationService.getOrCreateUserConversation(
        userContext.value.user_id,
        {
          current_page: userContext.value.current_page,
          profile_type: userContext.value.profile_type
        }
      );

      currentConversation.value = conversation;
      conversationId.value = conversation.id;

      // Load existing messages if any
      if (conversation.message_count > 0) {
        await loadConversationMessages(conversation.id);
      }
    } catch (error) {
      console.error('Error loading conversation:', error);
    } finally {
      isLoadingHistory.value = false;
    }
  };

  const loadConversationMessages = async (convId: string) => {
    try {
      const aiMessages = await AIConversationService.getConversationMessages(convId);

      // Convert AI messages to chat messages
      const chatMessages: ChatMessage[] = aiMessages.map(msg => ({
        id: msg.id,
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
        timestamp: new Date(msg.created_at),
        actions: msg.actions,
        suggestions: msg.suggestions
      }));

      messages.value = chatMessages;
    } catch (error) {
      console.error('Error loading conversation messages:', error);
    }
  };

  const saveMessage = async (message: ChatMessage) => {
    if (!conversationId.value || !userContext.value.is_authenticated) {
      return;
    }

    try {
      await AIConversationService.addMessageWithEmbedding(
        conversationId.value,
        message.role,
        message.content,
        {
          metadata: {
            timestamp: message.timestamp.toISOString(),
            user_context: userContext.value
          },
          actions: message.actions || [],
          suggestions: message.suggestions || []
        }
      );

      // Update conversation title if this is the first user message
      if (message.role === 'user' && messages.value.filter(m => m.role === 'user').length === 1) {
        const title = await AIConversationService.generateConversationTitle(conversationId.value);
        await AIConversationService.updateConversation(conversationId.value, { title });

        if (currentConversation.value) {
          currentConversation.value.title = title;
        }
      }
    } catch (error) {
      console.error('Error saving message:', error);
    }
  };

  const loadUserConversations = async () => {
    if (!userContext.value.is_authenticated || !userContext.value.user_id) {
      return;
    }

    try {
      isLoadingHistory.value = true;
      const conversations = await AIConversationService.getUserConversations(userContext.value.user_id);
      conversationHistory.value = conversations;
    } catch (error) {
      console.error('Error loading user conversations:', error);
    } finally {
      isLoadingHistory.value = false;
    }
  };

  const switchToConversation = async (convId: string) => {
    try {
      const conversation = await AIConversationService.getConversation(convId);
      if (conversation) {
        currentConversation.value = conversation;
        conversationId.value = convId;
        await loadConversationMessages(convId);
      }
    } catch (error) {
      console.error('Error switching conversation:', error);
    }
  };

  const startNewConversation = async () => {
    if (!userContext.value.is_authenticated || !userContext.value.user_id) {
      return;
    }

    try {
      const conversation = await AIConversationService.createConversation(
        userContext.value.user_id,
        undefined,
        {
          current_page: userContext.value.current_page,
          profile_type: userContext.value.profile_type
        }
      );

      currentConversation.value = conversation;
      conversationId.value = conversation.id;
      messages.value = [];

      // Initialize with welcome message
      initializeChat();
    } catch (error) {
      console.error('Error creating new conversation:', error);
    }
  };

  // Send AI message with streaming support
  const sendAIMessage = async (
    messageText: string,
    onChunk?: (content: string) => void,
    onComplete?: (actions: EnhancedActionButton[], suggestions: string[]) => void
  ): Promise<ChatMessage> => {
    if (!messageText.trim()) {
      throw new Error('Message cannot be empty');
    }

    // Add user message
    const userMessage = addMessage({
      role: 'user',
      content: messageText.trim()
    });

    setLoading(true);

    try {
      // Build enhanced request
      const request: EnhancedChatRequest = {
        message: messageText,
        conversation_history: messageHistory.value.slice(-10), // Last 10 messages for context
        user_context: userContext.value
      };

      // Create initial AI message for streaming
      const aiMessage = addMessage({
        role: 'assistant',
        content: ''
      });

      if (onChunk && onComplete) {
        // Use streaming
        await sendEnhancedChatMessageStream(
          request,
          (content: string) => {
            const currentMessage = messages.value.find(msg => msg.id === aiMessage.id);
            updateMessage(aiMessage.id, {
              content: (currentMessage?.content || '') + content
            });
            onChunk(content);
          },
          (actions: EnhancedActionButton[], suggestions: string[]) => {
            updateMessage(aiMessage.id, {
              actions,
              suggestions
            });
            onComplete(actions, suggestions);
          }
        );
      } else {
        // Use non-streaming
        const response = await sendEnhancedChatMessage(request);
        updateMessage(aiMessage.id, {
          content: response.response,
          actions: response.actions,
          suggestions: response.suggestions
        });
      }

      return aiMessage;
    } catch (error) {
      console.error('Error sending AI message:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };



  // Initialize with welcome message if no messages exist
  const initializeChat = () => {
    if (messages.value.length === 0) {
      const welcomeMessage = userContext.value.is_authenticated
        ? `Hello! I'm your ZbInnovation AI Assistant. I can help you navigate the platform, find opportunities, and connect with the right people. What would you like to know?`
        : `Welcome to ZbInnovation! I'm your AI Assistant. I can help you learn about our innovation ecosystem and guide you through getting started. How can I assist you today?`;

      addMessage({
        role: 'assistant',
        content: welcomeMessage,
        suggestions: userContext.value.is_authenticated
          ? [
              "How can I improve my profile?",
              "Show me networking opportunities",
              "Help me find relevant events",
              "What funding options are available?"
            ]
          : [
              "How do I sign up for the platform?",
              "What features are available?",
              "Tell me about the innovation community",
              "How can I connect with investors?"
            ]
      });
    }
  };

  return {
    // State
    isOpen,
    isLoading,
    hasUnreadMessage,
    messages,
    conversationId,
    currentConversation,
    conversationHistory,
    isLoadingHistory,

    // Computed
    userContext,
    messageHistory,
    lastMessage,

    // Actions
    toggleChat,
    openChat,
    closeChat,
    addMessage,
    updateMessage,
    clearMessages,
    setLoading,
    setConversationId,
    initializeChat,
    sendAIMessage,

    // Persistence actions
    loadOrCreateConversation,
    loadConversationMessages,
    saveMessage,
    loadUserConversations,
    switchToConversation,
    startNewConversation
  };
});
